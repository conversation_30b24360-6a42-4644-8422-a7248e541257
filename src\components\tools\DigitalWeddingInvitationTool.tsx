'use client';

import React, { useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import html2canvas from 'html2canvas';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { Calendar, MapPin, Heart, Eye, Copy, RefreshCw, Download } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const FormSchema = z.object({
  template: z.enum(['romantic', 'elegant', 'modern', 'classic', 'floral', 'luxury']).default('romantic'),
  groomName: z.string().min(1, 'اسم العريس مطلوب.'),
  brideName: z.string().min(1, 'اسم العروس مطلوب.'),
  weddingDate: z.string().min(1, 'تاريخ الزفاف مطلوب.'),
  weddingTime: z.string().min(1, 'وقت الزفاف مطلوب.'),
  venue: z.string().min(1, 'مكان الحفل مطلوب.'),
  venueAddress: z.string().min(1, 'عنوان المكان مطلوب.'),
  message: z.string().min(1, 'رسالة الدعوة مطلوبة.').default('نتشرف بحضوركم لمشاركتنا أجمل لحظات حياتنا'),
  hostFamily: z.string().min(1, 'اسم العائلة المضيفة مطلوب.'),
  rsvpPhone: z.string().optional(),
  rsvpEmail: z.string().email('البريد الإلكتروني غير صحيح').optional().or(z.literal('')),
});

type FormValues = z.infer<typeof FormSchema>;

// Digital Wedding Invitation Templates
function RomanticTemplate({ data }: { data: FormValues }) {
  return (
    <div className="w-full max-w-md min-h-[600px] bg-gradient-to-br from-pink-50 via-rose-50 to-pink-100 rounded-2xl shadow-2xl flex flex-col invitation-preview p-6 text-center relative overflow-hidden border border-rose-200" dir="rtl">
      {/* Background Hearts */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 text-rose-300">
          <Heart size={20} fill="currentColor" />
        </div>
        <div className="absolute top-20 right-8 text-rose-300">
          <Heart size={16} fill="currentColor" />
        </div>
        <div className="absolute bottom-20 left-6 text-rose-300">
          <Heart size={18} fill="currentColor" />
        </div>
        <div className="absolute bottom-32 right-12 text-rose-300">
          <Heart size={14} fill="currentColor" />
        </div>
      </div>

      <div className="relative z-10 flex flex-col h-full space-y-4">
        {/* Header */}
        <div className="mb-6">
          <div className="flex justify-center items-center mb-4">
            <Heart size={24} className="text-rose-500 mx-2" fill="currentColor" />
            <h1 className="text-2xl font-bold text-rose-600 invitation-title">دعوة زفاف</h1>
            <Heart size={24} className="text-rose-500 mx-2" fill="currentColor" />
          </div>
          <p className="text-sm text-rose-400 leading-relaxed">{data.message}</p>
        </div>

        {/* Names */}
        <div>
          <h2 className="text-3xl font-bold text-rose-700 invitation-title mb-2">{data.groomName}</h2>
          <div className="flex justify-center items-center my-3">
            <div className="w-8 h-px bg-rose-400"></div>
            <Heart size={16} className="mx-3 text-rose-500" fill="currentColor" />
            <div className="w-8 h-px bg-rose-400"></div>
          </div>
          <h2 className="text-3xl font-bold text-rose-700 invitation-title">{data.brideName}</h2>
        </div>

        {/* Event Details */}
        <div className="my-auto space-y-4">
          <div className="bg-white/70 rounded-lg p-4 border border-rose-200">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Calendar size={16} className="text-rose-600" />
              <p className="font-bold text-rose-700">التاريخ والوقت</p>
            </div>
            <p className="text-rose-600 text-sm">{data.weddingDate}</p>
            <p className="text-rose-600 text-sm">{data.weddingTime}</p>
          </div>

          <div className="bg-white/70 rounded-lg p-4 border border-rose-200">
            <div className="flex items-center justify-center gap-2 mb-2">
              <MapPin size={16} className="text-rose-600" />
              <p className="font-bold text-rose-700">المكان</p>
            </div>
            <p className="text-rose-600 text-sm font-medium">{data.venue}</p>
            <p className="text-rose-500 text-xs">{data.venueAddress}</p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-auto">
          <div className="bg-rose-100/80 rounded-lg p-3 border border-rose-200 mb-4">
            <p className="text-xs text-rose-600">بانتظار حضوركم الكريم</p>
            <p className="text-sm font-bold text-rose-700">{data.hostFamily}</p>
          </div>
          
          {(data.rsvpPhone || data.rsvpEmail) && (
            <div className="text-xs text-rose-500">
              <p>للتأكيد:</p>
              {data.rsvpPhone && <p>📱 {data.rsvpPhone}</p>}
              {data.rsvpEmail && <p>📧 {data.rsvpEmail}</p>}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function ElegantTemplate({ data }: { data: FormValues }) {
  return (
    <div className="w-full max-w-md min-h-[600px] bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 rounded-2xl shadow-2xl flex flex-col invitation-preview p-6 text-center text-white relative overflow-hidden border border-gray-700" dir="rtl">
      {/* Elegant Pattern */}
      <div className="absolute inset-0 opacity-5">
        <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="elegant-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
              <circle cx="10" cy="10" r="2" fill="#FFD700" opacity="0.3"/>
              <path d="M5,5 L15,15 M15,5 L5,15" stroke="#FFD700" strokeWidth="0.5" opacity="0.2"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#elegant-pattern)"/>
        </svg>
      </div>

      <div className="relative z-10 flex flex-col h-full space-y-4">
        {/* Header */}
        <div>
          <div className="border-b-2 border-yellow-400 pb-4 mb-4">
            <h1 className="text-2xl font-bold text-yellow-400 invitation-title">دعوة زفاف</h1>
            <div className="flex justify-center mt-2">
              <div className="w-16 h-1 bg-gradient-to-r from-transparent via-yellow-400 to-transparent"></div>
            </div>
          </div>
          <p className="text-sm text-gray-300 leading-relaxed">{data.message}</p>
        </div>

        {/* Names */}
        <div className="my-6">
          <h2 className="text-3xl font-bold text-yellow-400 invitation-title mb-3">{data.groomName}</h2>
          <p className="text-xl text-yellow-300 my-2">و</p>
          <h2 className="text-3xl font-bold text-yellow-400 invitation-title">{data.brideName}</h2>
        </div>

        {/* Event Details */}
        <div className="my-auto space-y-4">
          <div className="bg-gray-800/50 rounded-lg p-4 border border-yellow-400/30">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Calendar size={16} className="text-yellow-400" />
              <p className="font-bold text-yellow-400">التاريخ والوقت</p>
            </div>
            <p className="text-gray-200 text-sm">{data.weddingDate}</p>
            <p className="text-gray-200 text-sm">{data.weddingTime}</p>
          </div>

          <div className="bg-gray-800/50 rounded-lg p-4 border border-yellow-400/30">
            <div className="flex items-center justify-center gap-2 mb-2">
              <MapPin size={16} className="text-yellow-400" />
              <p className="font-bold text-yellow-400">المكان</p>
            </div>
            <p className="text-gray-200 text-sm font-medium">{data.venue}</p>
            <p className="text-gray-300 text-xs">{data.venueAddress}</p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-auto">
          <div className="bg-gray-800/50 rounded-lg p-3 border border-yellow-400/50 mb-4">
            <p className="text-xs text-gray-300">بانتظار حضوركم الكريم</p>
            <p className="text-sm font-bold text-yellow-400">{data.hostFamily}</p>
          </div>
          
          {(data.rsvpPhone || data.rsvpEmail) && (
            <div className="text-xs text-gray-400">
              <p>للتأكيد:</p>
              {data.rsvpPhone && <p>📱 {data.rsvpPhone}</p>}
              {data.rsvpEmail && <p>📧 {data.rsvpEmail}</p>}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function ModernTemplate({ data }: { data: FormValues }) {
  return (
    <div className="w-full max-w-md min-h-[600px] bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-2xl shadow-2xl flex flex-col invitation-preview p-6 text-center relative overflow-hidden border border-blue-200" dir="rtl">
      {/* Modern Geometric Background */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="modern-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse">
              <polygon points="12.5,5 20,12.5 12.5,20 5,12.5" fill="none" stroke="#4F46E5" strokeWidth="1" opacity="0.4"/>
              <circle cx="12.5" cy="12.5" r="3" fill="#4F46E5" opacity="0.2"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#modern-pattern)"/>
        </svg>
      </div>

      <div className="relative z-10 flex flex-col h-full space-y-4">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-indigo-600 invitation-title mb-4">دعوة زفاف</h1>
          <p className="text-sm text-indigo-500 leading-relaxed">{data.message}</p>
        </div>

        {/* Names */}
        <div className="my-6">
          <div className="bg-white/80 rounded-xl p-4 border border-indigo-200 shadow-sm">
            <h2 className="text-2xl font-bold text-indigo-700 invitation-title mb-2">{data.groomName}</h2>
            <div className="flex justify-center items-center my-2">
              <div className="w-6 h-px bg-indigo-400"></div>
              <span className="mx-2 text-indigo-500">♥</span>
              <div className="w-6 h-px bg-indigo-400"></div>
            </div>
            <h2 className="text-2xl font-bold text-indigo-700 invitation-title">{data.brideName}</h2>
          </div>
        </div>

        {/* Event Details */}
        <div className="my-auto space-y-3">
          <div className="bg-white/80 rounded-lg p-3 border border-indigo-200">
            <div className="flex items-center justify-center gap-2 mb-1">
              <Calendar size={14} className="text-indigo-600" />
              <p className="font-bold text-indigo-700 text-sm">التاريخ والوقت</p>
            </div>
            <p className="text-indigo-600 text-xs">{data.weddingDate}</p>
            <p className="text-indigo-600 text-xs">{data.weddingTime}</p>
          </div>

          <div className="bg-white/80 rounded-lg p-3 border border-indigo-200">
            <div className="flex items-center justify-center gap-2 mb-1">
              <MapPin size={14} className="text-indigo-600" />
              <p className="font-bold text-indigo-700 text-sm">المكان</p>
            </div>
            <p className="text-indigo-600 text-xs font-medium">{data.venue}</p>
            <p className="text-indigo-500 text-xs">{data.venueAddress}</p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-auto">
          <div className="bg-indigo-100/80 rounded-lg p-3 border border-indigo-200 mb-3">
            <p className="text-xs text-indigo-600">بانتظار حضوركم الكريم</p>
            <p className="text-sm font-bold text-indigo-700">{data.hostFamily}</p>
          </div>

          {(data.rsvpPhone || data.rsvpEmail) && (
            <div className="text-xs text-indigo-500">
              <p>للتأكيد:</p>
              {data.rsvpPhone && <p>📱 {data.rsvpPhone}</p>}
              {data.rsvpEmail && <p>📧 {data.rsvpEmail}</p>}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function ClassicTemplate({ data }: { data: FormValues }) {
  return (
    <div className="w-full max-w-md min-h-[600px] bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 rounded-2xl shadow-2xl flex flex-col invitation-preview p-6 text-center relative overflow-hidden border border-amber-200" dir="rtl">
      {/* Classic Ornamental Background */}
      <div className="absolute inset-0 opacity-8">
        <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="classic-pattern" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse">
              <path d="M15,5 L25,15 L15,25 L5,15 Z" fill="none" stroke="#D97706" strokeWidth="1" opacity="0.3"/>
              <circle cx="15" cy="15" r="2" fill="#D97706" opacity="0.2"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#classic-pattern)"/>
        </svg>
      </div>

      <div className="relative z-10 flex flex-col h-full space-y-4">
        {/* Header */}
        <div>
          <div className="border-b-2 border-amber-600 pb-4 mb-4">
            <h1 className="text-2xl font-bold text-amber-700 invitation-title">دعوة زفاف</h1>
            <div className="flex justify-center mt-2">
              <div className="w-16 h-1 bg-gradient-to-r from-transparent via-amber-600 to-transparent"></div>
            </div>
          </div>
          <p className="text-sm text-amber-600 leading-relaxed">{data.message}</p>
        </div>

        {/* Names */}
        <div className="my-6">
          <div className="bg-white/90 rounded-lg p-4 border-2 border-amber-300 shadow-sm">
            <h2 className="text-3xl font-bold text-amber-800 invitation-title mb-3">{data.groomName}</h2>
            <div className="flex justify-center items-center my-3">
              <div className="w-8 h-px bg-amber-600"></div>
              <span className="mx-3 text-xl text-amber-600">و</span>
              <div className="w-8 h-px bg-amber-600"></div>
            </div>
            <h2 className="text-3xl font-bold text-amber-800 invitation-title">{data.brideName}</h2>
          </div>
        </div>

        {/* Event Details */}
        <div className="my-auto space-y-4">
          <div className="bg-white/90 rounded-lg p-4 border border-amber-300">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Calendar size={16} className="text-amber-700" />
              <p className="font-bold text-amber-800">التاريخ والوقت</p>
            </div>
            <p className="text-amber-700 text-sm">{data.weddingDate}</p>
            <p className="text-amber-700 text-sm">{data.weddingTime}</p>
          </div>

          <div className="bg-white/90 rounded-lg p-4 border border-amber-300">
            <div className="flex items-center justify-center gap-2 mb-2">
              <MapPin size={16} className="text-amber-700" />
              <p className="font-bold text-amber-800">المكان</p>
            </div>
            <p className="text-amber-700 text-sm font-medium">{data.venue}</p>
            <p className="text-amber-600 text-xs">{data.venueAddress}</p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-auto">
          <div className="bg-amber-100/90 rounded-lg p-3 border border-amber-300 mb-4">
            <p className="text-xs text-amber-700">بانتظار حضوركم الكريم</p>
            <p className="text-sm font-bold text-amber-800">{data.hostFamily}</p>
          </div>

          {(data.rsvpPhone || data.rsvpEmail) && (
            <div className="text-xs text-amber-600">
              <p>للتأكيد:</p>
              {data.rsvpPhone && <p>📱 {data.rsvpPhone}</p>}
              {data.rsvpEmail && <p>📧 {data.rsvpEmail}</p>}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function FloralTemplate({ data }: { data: FormValues }) {
  return (
    <div className="w-full max-w-md min-h-[600px] bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-2xl shadow-2xl flex flex-col invitation-preview p-6 text-center relative overflow-hidden border border-green-200" dir="rtl">
      {/* Floral Background */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="floral-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M20 10c5 0 10 5 10 10s-5 10-10 10-10-5-10-10 5-10 10-10z" fill="none" stroke="#059669" strokeWidth="1" opacity="0.4"/>
              <path d="M20 20c0-5 5-10 10-10s10 5 10 10-5 10-10 10-10-5-10-10z" fill="none" stroke="#059669" strokeWidth="1" opacity="0.4"/>
              <path d="M20 20c0 5-5 10-10 10s-10-5-10-10 5-10 10-10 10 5 10 10z" fill="none" stroke="#059669" strokeWidth="1" opacity="0.4"/>
              <path d="M20 20c-5 0-10-5-10-10s5-10 10-10 10 5 10 10-5 10-10 10z" fill="none" stroke="#059669" strokeWidth="1" opacity="0.4"/>
              <circle cx="20" cy="20" r="3" fill="#059669" opacity="0.3"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#floral-pattern)"/>
        </svg>
      </div>

      <div className="relative z-10 flex flex-col h-full space-y-4">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-emerald-700 invitation-title mb-4">🌸 دعوة زفاف 🌸</h1>
          <p className="text-sm text-emerald-600 leading-relaxed">{data.message}</p>
        </div>

        {/* Names */}
        <div className="my-6">
          <div className="bg-white/90 rounded-xl p-4 border-2 border-emerald-200 shadow-sm">
            <h2 className="text-2xl font-bold text-emerald-800 invitation-title mb-2">{data.groomName}</h2>
            <div className="flex justify-center items-center my-2">
              <span className="text-emerald-600">🌿</span>
              <div className="w-6 h-px bg-emerald-400 mx-2"></div>
              <span className="text-emerald-600">💚</span>
              <div className="w-6 h-px bg-emerald-400 mx-2"></div>
              <span className="text-emerald-600">🌿</span>
            </div>
            <h2 className="text-2xl font-bold text-emerald-800 invitation-title">{data.brideName}</h2>
          </div>
        </div>

        {/* Event Details */}
        <div className="my-auto space-y-3">
          <div className="bg-white/90 rounded-lg p-3 border border-emerald-200">
            <div className="flex items-center justify-center gap-2 mb-1">
              <Calendar size={14} className="text-emerald-700" />
              <p className="font-bold text-emerald-800 text-sm">🗓️ التاريخ والوقت</p>
            </div>
            <p className="text-emerald-700 text-xs">{data.weddingDate}</p>
            <p className="text-emerald-700 text-xs">{data.weddingTime}</p>
          </div>

          <div className="bg-white/90 rounded-lg p-3 border border-emerald-200">
            <div className="flex items-center justify-center gap-2 mb-1">
              <MapPin size={14} className="text-emerald-700" />
              <p className="font-bold text-emerald-800 text-sm">📍 المكان</p>
            </div>
            <p className="text-emerald-700 text-xs font-medium">{data.venue}</p>
            <p className="text-emerald-600 text-xs">{data.venueAddress}</p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-auto">
          <div className="bg-emerald-100/90 rounded-lg p-3 border border-emerald-200 mb-3">
            <p className="text-xs text-emerald-700">🌺 بانتظار حضوركم الكريم 🌺</p>
            <p className="text-sm font-bold text-emerald-800">{data.hostFamily}</p>
          </div>

          {(data.rsvpPhone || data.rsvpEmail) && (
            <div className="text-xs text-emerald-600">
              <p>للتأكيد:</p>
              {data.rsvpPhone && <p>📱 {data.rsvpPhone}</p>}
              {data.rsvpEmail && <p>📧 {data.rsvpEmail}</p>}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function LuxuryTemplate({ data }: { data: FormValues }) {
  return (
    <div className="w-full max-w-md min-h-[600px] bg-gradient-to-br from-purple-900 via-indigo-900 to-blue-900 rounded-2xl shadow-2xl flex flex-col invitation-preview p-6 text-center text-white relative overflow-hidden border-2 border-purple-400" dir="rtl">
      {/* Luxury Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="luxury-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse">
              <path d="M12.5,5 L20,12.5 L12.5,20 L5,12.5 Z" fill="none" stroke="#A855F7" strokeWidth="1" opacity="0.6"/>
              <circle cx="12.5" cy="12.5" r="2" fill="#A855F7" opacity="0.4"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#luxury-pattern)"/>
        </svg>
      </div>

      <div className="relative z-10 flex flex-col h-full space-y-4">
        {/* Header */}
        <div>
          <div className="border-b-2 border-purple-400 pb-4 mb-4">
            <h1 className="text-2xl font-bold text-purple-300 invitation-title">✨ دعوة زفاف فاخرة ✨</h1>
            <div className="flex justify-center mt-2">
              <div className="w-16 h-1 bg-gradient-to-r from-transparent via-purple-400 to-transparent"></div>
            </div>
          </div>
          <p className="text-sm text-purple-200 leading-relaxed">{data.message}</p>
        </div>

        {/* Names */}
        <div className="my-6">
          <div className="bg-black/40 rounded-xl p-4 border-2 border-purple-400 shadow-2xl backdrop-blur-sm">
            <h2 className="text-3xl font-bold text-purple-300 invitation-title mb-3">{data.groomName}</h2>
            <div className="flex justify-center items-center my-3">
              <div className="w-8 h-px bg-purple-400"></div>
              <span className="mx-3 text-xl text-purple-300">💎</span>
              <div className="w-8 h-px bg-purple-400"></div>
            </div>
            <h2 className="text-3xl font-bold text-purple-300 invitation-title">{data.brideName}</h2>
          </div>
        </div>

        {/* Event Details */}
        <div className="my-auto space-y-4">
          <div className="bg-black/30 rounded-lg p-4 border border-purple-400 backdrop-blur-sm">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Calendar size={16} className="text-purple-300" />
              <p className="font-bold text-purple-200">التاريخ والوقت</p>
            </div>
            <p className="text-purple-100 text-sm">{data.weddingDate}</p>
            <p className="text-purple-100 text-sm">{data.weddingTime}</p>
          </div>

          <div className="bg-black/30 rounded-lg p-4 border border-purple-400 backdrop-blur-sm">
            <div className="flex items-center justify-center gap-2 mb-2">
              <MapPin size={16} className="text-purple-300" />
              <p className="font-bold text-purple-200">المكان</p>
            </div>
            <p className="text-purple-100 text-sm font-medium">{data.venue}</p>
            <p className="text-purple-200 text-xs">{data.venueAddress}</p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-auto">
          <div className="bg-black/40 rounded-lg p-3 border border-purple-400 mb-4 backdrop-blur-sm">
            <p className="text-xs text-purple-200">✨ بانتظار حضوركم الكريم ✨</p>
            <p className="text-sm font-bold text-purple-300">{data.hostFamily}</p>
          </div>

          {(data.rsvpPhone || data.rsvpEmail) && (
            <div className="text-xs text-purple-300">
              <p>للتأكيد:</p>
              {data.rsvpPhone && <p>📱 {data.rsvpPhone}</p>}
              {data.rsvpEmail && <p>📧 {data.rsvpEmail}</p>}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export function DigitalWeddingInvitationTool() {
  const { toast } = useToast();
  const invitationRef = useRef<HTMLDivElement>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    mode: 'onChange',
    defaultValues: {
        template: 'romantic',
        groomName: 'أحمد',
        brideName: 'فاطمة',
        weddingDate: '١٥ شعبان ١٤٤٦ هـ',
        weddingTime: 'الساعة ٨:٠٠ مساءً',
        venue: 'قاعة الأفراح الذهبية',
        venueAddress: 'شارع الملك فهد، الرياض',
        message: 'نتشرف بحضوركم لمشاركتنا أجمل لحظات حياتنا',
        hostFamily: 'عائلة الأحمد وعائلة المحمد',
        rsvpPhone: '٠٥٠١٢٣٤٥٦٧',
        rsvpEmail: '',
    },
  });

  const formData = form.watch();



  const renderTemplate = () => {
    switch (formData.template) {
      case 'romantic':
        return <RomanticTemplate data={formData} />;
      case 'elegant':
        return <ElegantTemplate data={formData} />;
      case 'modern':
        return <ModernTemplate data={formData} />;
      case 'classic':
        return <ClassicTemplate data={formData} />;
      case 'floral':
        return <FloralTemplate data={formData} />;
      case 'luxury':
        return <LuxuryTemplate data={formData} />;
      default:
        return <RomanticTemplate data={formData} />;
    }
  };



  const copyInvitationText = () => {
    const invitationText = `
🌸 دعوة زفاف 🌸

${formData.message}

${formData.groomName} ♥ ${formData.brideName}

📅 التاريخ: ${formData.weddingDate}
🕐 الوقت: ${formData.weddingTime}
📍 المكان: ${formData.venue}
📍 العنوان: ${formData.venueAddress}

بانتظار حضوركم الكريم
${formData.hostFamily}

${formData.rsvpPhone ? `📱 للتأكيد: ${formData.rsvpPhone}` : ''}
${formData.rsvpEmail ? `📧 للتأكيد: ${formData.rsvpEmail}` : ''}`;

    navigator.clipboard.writeText(invitationText).then(() => {
      toast({
        title: "تم نسخ النص",
        description: "تم نسخ نص الدعوة إلى الحافظة.",
      });
    }).catch(() => {
      toast({
        title: "خطأ في النسخ",
        description: "لم نتمكن من نسخ النص.",
      });
    });
  };

  const downloadAsImage = async () => {
    if (!invitationRef.current) return;

    try {
      toast({
        title: "جاري إنشاء الصورة...",
        description: "يرجى الانتظار قليلاً.",
      });

      const canvas = await html2canvas(invitationRef.current, {
        useCORS: true,
        allowTaint: true,
        width: invitationRef.current.offsetWidth * 2,
        height: invitationRef.current.offsetHeight * 2,
      });

      // تحويل إلى صورة وتحميلها
      const link = document.createElement('a');
      link.download = `دعوة-زفاف-${formData.groomName}-${formData.brideName}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();

      toast({
        title: "تم تحميل الصورة",
        description: "تم حفظ دعوة الزفاف كصورة بنجاح.",
      });
    } catch (error) {
      console.error('Error generating image:', error);
      toast({
        title: "خطأ في إنشاء الصورة",
        description: "حدث خطأ أثناء إنشاء الصورة. يرجى المحاولة مرة أخرى.",
      });
    }
  };

  const resetForm = () => {
    form.reset({
        template: 'romantic',
        groomName: '',
        brideName: '',
        weddingDate: '',
        weddingTime: '',
        venue: '',
        venueAddress: '',
        message: 'نتشرف بحضوركم لمشاركتنا أجمل لحظات حياتنا',
        hostFamily: '',
        rsvpPhone: '',
        rsvpEmail: '',
    });
    toast({
      title: "تم إعادة تعيين النموذج",
      description: "تم مسح جميع البيانات بنجاح.",
    });
  };



  return (
    <>
      <div className="space-y-8">
        {/* Form Section */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                إنشاء دعوة زفاف رقمية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form className="space-y-6">
                  {/* Template Selection */}
                  <FormField
                    control={form.control}
                    name="template"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>اختر التصميم</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            value={field.value}
                            className="grid grid-cols-2 md:grid-cols-3 gap-2"
                          >
                            <Label className={cn("border rounded-md p-2 flex items-center justify-center gap-1 cursor-pointer hover:bg-muted/50 transition-colors text-xs", field.value === 'romantic' && "bg-primary/10 border-primary")}>
                              <RadioGroupItem value="romantic" className="sr-only" />
                              <span>💕 رومانسي</span>
                            </Label>
                            <Label className={cn("border rounded-md p-2 flex items-center justify-center gap-1 cursor-pointer hover:bg-muted/50 transition-colors text-xs", field.value === 'elegant' && "bg-primary/10 border-primary")}>
                              <RadioGroupItem value="elegant" className="sr-only" />
                              <span>✨ أنيق</span>
                            </Label>
                            <Label className={cn("border rounded-md p-2 flex items-center justify-center gap-1 cursor-pointer hover:bg-muted/50 transition-colors text-xs", field.value === 'modern' && "bg-primary/10 border-primary")}>
                              <RadioGroupItem value="modern" className="sr-only" />
                              <span>🔷 عصري</span>
                            </Label>
                            <Label className={cn("border rounded-md p-2 flex items-center justify-center gap-1 cursor-pointer hover:bg-muted/50 transition-colors text-xs", field.value === 'classic' && "bg-primary/10 border-primary")}>
                              <RadioGroupItem value="classic" className="sr-only" />
                              <span>🏛️ كلاسيكي</span>
                            </Label>
                            <Label className={cn("border rounded-md p-2 flex items-center justify-center gap-1 cursor-pointer hover:bg-muted/50 transition-colors text-xs", field.value === 'floral' && "bg-primary/10 border-primary")}>
                              <RadioGroupItem value="floral" className="sr-only" />
                              <span>🌸 زهري</span>
                            </Label>
                            <Label className={cn("border rounded-md p-2 flex items-center justify-center gap-1 cursor-pointer hover:bg-muted/50 transition-colors text-xs", field.value === 'luxury' && "bg-primary/10 border-primary")}>
                              <RadioGroupItem value="luxury" className="sr-only" />
                              <span>💎 فاخر</span>
                            </Label>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="groomName" render={({ field }) => (
                      <FormItem>
                        <FormLabel>اسم العريس</FormLabel>
                        <FormControl>
                          <Input placeholder="أحمد" {...field} dir="rtl" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />

                    <FormField control={form.control} name="brideName" render={({ field }) => (
                      <FormItem>
                        <FormLabel>اسم العروس</FormLabel>
                        <FormControl>
                          <Input placeholder="فاطمة" {...field} dir="rtl" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="weddingDate" render={({ field }) => (
                      <FormItem>
                        <FormLabel>تاريخ الزفاف</FormLabel>
                        <FormControl>
                          <Input placeholder="١٥ شعبان ١٤٤٦ هـ" {...field} dir="rtl" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />

                    <FormField control={form.control} name="weddingTime" render={({ field }) => (
                      <FormItem>
                        <FormLabel>وقت الزفاف</FormLabel>
                        <FormControl>
                          <Input placeholder="الساعة ٨:٠٠ مساءً" {...field} dir="rtl" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>

                  <FormField control={form.control} name="venue" render={({ field }) => (
                    <FormItem>
                      <FormLabel>مكان الحفل</FormLabel>
                      <FormControl>
                        <Input placeholder="قاعة الأفراح الذهبية" {...field} dir="rtl" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />

                  <FormField control={form.control} name="venueAddress" render={({ field }) => (
                    <FormItem>
                      <FormLabel>عنوان المكان</FormLabel>
                      <FormControl>
                        <Input placeholder="شارع الملك فهد، الرياض" {...field} dir="rtl" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />

                  <FormField control={form.control} name="message" render={({ field }) => (
                    <FormItem>
                      <FormLabel>رسالة الدعوة</FormLabel>
                      <FormControl>
                        <Textarea {...field} className="min-h-[80px]" dir="rtl" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />

                  <FormField control={form.control} name="hostFamily" render={({ field }) => (
                    <FormItem>
                      <FormLabel>العائلة المضيفة</FormLabel>
                      <FormControl>
                        <Input placeholder="عائلة الأحمد وعائلة المحمد" {...field} dir="rtl" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="rsvpPhone" render={({ field }) => (
                      <FormItem>
                        <FormLabel>رقم التأكيد (اختياري)</FormLabel>
                        <FormControl>
                          <Input placeholder="٠٥٠١٢٣٤٥٦٧" {...field} dir="rtl" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />

                    <FormField control={form.control} name="rsvpEmail" render={({ field }) => (
                      <FormItem>
                        <FormLabel>بريد التأكيد (اختياري)</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} type="email" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        {/* Preview Section */}
        <div>
          <Card className="bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-dashed border-gray-300">
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-2 text-xl">
                <Eye className="h-6 w-6 text-primary" />
                معاينة الدعوة الرقمية
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-2">
                هذه معاينة لدعوة الزفاف الرقمية التي ستظهر للضيوف
              </p>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <div ref={invitationRef} className="transform hover:scale-105 transition-transform duration-300">
                {renderTemplate()}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
          <Button variant="default" onClick={downloadAsImage} className="w-full sm:w-auto">
            <Download className="ml-2 h-4 w-4" />
            حفظ كصورة
          </Button>
          <Button variant="outline" onClick={copyInvitationText} className="w-full sm:w-auto">
            <Copy className="ml-2 h-4 w-4" />
            نسخ النص
          </Button>
          <Button variant="ghost" onClick={resetForm} className="w-full sm:w-auto">
            <RefreshCw className="ml-2 h-4 w-4" />
            إعادة تعيين
          </Button>
        </div>
      </div>
    </>
  );
}
