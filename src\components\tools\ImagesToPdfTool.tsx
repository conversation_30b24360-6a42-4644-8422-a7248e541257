
'use client';

import { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Upload, Download, Trash2, AlertTriangle, Images, Loader2, File as FileIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { saveAs } from 'file-saver';
import { jsPDF } from 'jspdf';
import { X, GripVertical, ChevronUp, ChevronDown } from 'lucide-react';

interface ImageFile {
  file: File;
  id: string;
  name: string;
  dataUrl: string;
}

export function ImagesToPdfTool() {
  const [selectedImages, setSelectedImages] = useState<ImageFile[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
    if (imageFiles.length === 0) {
      setError('يرجى اختيار ملفات صور صالحة.');
      return;
    }

    Promise.all(imageFiles.map(file => new Promise<ImageFile>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => resolve({
        file,
        id: Math.random().toString(36),
        name: file.name,
        dataUrl: e.target?.result as string,
      });
      reader.onerror = reject;
      reader.readAsDataURL(file);
    }))).then(newImages => {
      setSelectedImages(prev => [...prev, ...newImages]);
      setError(null);
      toast({ title: 'تمت إضافة الصور', description: `تمت إضافة ${newImages.length} صورة.` });
    });
  };

  const convertToPdf = async () => {
    if (selectedImages.length === 0) {
      setError('يرجى إضافة صورة واحدة على الأقل.');
      return;
    }

    setIsConverting(true);
    toast({ title: 'جاري تحويل الصور إلى PDF...' });

    try {
      const pdf = new jsPDF('p', 'mm', 'a4');
      for (let i = 0; i < selectedImages.length; i++) {
        const imageFile = selectedImages[i];
        if (i > 0) pdf.addPage();
        
        const img = new Image();
        img.src = imageFile.dataUrl;
        await new Promise(resolve => img.onload = resolve);
        
        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();
        
        const ratio = img.width / img.height;
        let imgWidth = pageWidth - 20; // with margin
        let imgHeight = imgWidth / ratio;

        if (imgHeight > pageHeight - 20) {
          imgHeight = pageHeight - 20;
          imgWidth = imgHeight * ratio;
        }

        const x = (pageWidth - imgWidth) / 2;
        const y = (pageHeight - imgHeight) / 2;
        
        pdf.addImage(imageFile.dataUrl, imageFile.file.type.split('/')[1].toUpperCase(), x, y, imgWidth, imgHeight);
      }
      
      const pdfBlob = pdf.output('blob');
      saveAs(pdfBlob, 'converted_images.pdf');
      toast({ title: 'تم التحويل بنجاح!', description: 'تم تحميل ملف PDF.' });
    } catch (err) {
      setError('حدث خطأ أثناء إنشاء ملف PDF.');
    } finally {
      setIsConverting(false);
    }
  };
  
  const removeImage = (id: string) => {
    setSelectedImages(prev => prev.filter(img => img.id !== id));
  };

  const clearAll = () => {
    setSelectedImages([]);
    setError(null);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };
  
  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...selectedImages];
    const [movedItem] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedItem);
    setSelectedImages(newImages);
  };
  
  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>تحويل الصور إلى PDF</CardTitle>
        <CardDescription>اجمع صورك في ملف PDF واحد بسهولة وأمان.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="p-6 border-2 border-dashed rounded-lg text-center" onClick={() => fileInputRef.current?.click()}>
          <Images className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-600">اختر الصور أو اسحبها هنا (JPG, PNG, ...)</p>
          <input type="file" ref={fileInputRef} onChange={handleFileChange} accept="image/*" multiple className="hidden" />
        </div>

        {error && <Alert variant="destructive"><AlertTriangle className="h-4 w-4" /><AlertDescription>{error}</AlertDescription></Alert>}

        {selectedImages.length > 0 && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold">الصور المحددة ({selectedImages.length})</h3>
              <Button onClick={clearAll} variant="destructive" size="sm"><Trash2 className="ml-2 h-4 w-4" /> مسح الكل</Button>
            </div>
            <div className="space-y-2 max-h-80 overflow-y-auto p-2 border rounded-md">
              {selectedImages.map((image, index) => (
                <div key={image.id} className="flex items-center gap-3 p-2 bg-muted rounded-md" draggable onDragStart={e => handleDragStart(e, index)} onDragOver={e => e.preventDefault()} onDrop={() => moveImage(draggedIndex!, index)}>
                  <GripVertical className="cursor-move text-muted-foreground" />
                  <img src={image.dataUrl} alt={image.name} className="w-12 h-12 object-cover rounded-md" />
                  <p className="flex-1 text-sm truncate">{image.name}</p>
                  <Button onClick={() => removeImage(image.id)} variant="ghost" size="icon"><X className="h-4 w-4" /></Button>
                </div>
              ))}
            </div>
            <Button onClick={convertToPdf} disabled={isConverting} className="w-full">
              {isConverting ? <><Loader2 className="ml-2 h-4 w-4 animate-spin" /> جاري الإنشاء...</> : <><FileIcon className="ml-2 h-4 w-4" /> إنشاء ملف PDF</>}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
