
'use client';

import { useState, useRef, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Upload, Download, Trash2, AlertTriangle, FileText, ImageIcon, Loader2, Archive } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Label } from '../ui/label';

// Declare global types for PDF.js
declare global {
  interface Window {
    pdfjsLib: any;
  }
}

interface ConvertedImage {
  pageNumber: number;
  dataUrl: string;
  fileName: string;
  originalFile: string;
}

export function PdfToImagesTool() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [convertedImages, setConvertedImages] = useState<ConvertedImage[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progressText, setProgressText] = useState('');
  const [pdfLibLoaded, setPdfLibLoaded] = useState(false);
  const [imageFormat, setImageFormat] = useState<'png' | 'jpeg'>('png');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Load PDF.js library
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.pdfjsLib) {
      const script = document.createElement('script');
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
      script.onload = () => {
        if (window.pdfjsLib) {
          window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
          setPdfLibLoaded(true);
        }
      };
      script.onerror = () => setError('فشل تحميل مكتبة PDF.js. يرجى إعادة تحميل الصفحة.');
      document.head.appendChild(script);
    } else if (window.pdfjsLib) {
      setPdfLibLoaded(true);
    }
  }, []);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    if (file.type !== 'application/pdf') {
      setError('يرجى اختيار ملف PDF صالح.');
      return;
    }
    if (file.size > 10 * 1024 * 1024) {
      setError('حجم الملف كبير جدًا. يرجى اختيار ملف أصغر من 10 ميجابايت.');
      return;
    }

    setSelectedFile(file);
    setError(null);
    setConvertedImages([]);
    toast({ title: 'تم اختيار الملف', description: `تم اختيار ${file.name}.` });
  };

  const convertPDF = async () => {
    if (!selectedFile || !pdfLibLoaded) return;

    setIsConverting(true);
    setError(null);
    setConvertedImages([]);
    setProgressText('بدء المعالجة...');

    try {
      const allImages: ConvertedImage[] = [];
      const arrayBuffer = await selectedFile.arrayBuffer();
      const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      const numPages = pdf.numPages;

      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        setProgressText(`معالجة الصفحة ${pageNum} من ${numPages}...`);
        const page = await pdf.getPage(pageNum);
        const viewport = page.getViewport({ scale: 2.0 });
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        if (!context) throw new Error('لا يمكن إنشاء سياق للرسم.');

        canvas.height = viewport.height;
        canvas.width = viewport.width;

        await page.render({ canvasContext: context, viewport: viewport }).promise;

        const dataUrl = canvas.toDataURL(`image/${imageFormat}`, 0.95);
        const fileName = `${selectedFile.name.replace('.pdf', '')}_page_${pageNum}.${imageFormat}`;

        allImages.push({ pageNumber: pageNum, dataUrl, fileName, originalFile: selectedFile.name });
      }

      setConvertedImages(allImages);
      toast({ title: 'تم التحويل بنجاح!', description: `تم تحويل ${numPages} صفحة.` });
    } catch (err) {
      setError('فشل تحويل الملف. تأكد من أن الملف صالح وغير محمي بكلمة مرور.');
    } finally {
      setIsConverting(false);
      setProgressText('');
    }
  };

  const downloadAllAsZip = async () => {
    if (convertedImages.length === 0) return;

    toast({ title: 'جاري إنشاء ملف ZIP...', description: `يتم ضغط ${convertedImages.length} صورة.` });
    const zip = new JSZip();
    
    for (const image of convertedImages) {
      const response = await fetch(image.dataUrl);
      const blob = await response.blob();
      zip.file(image.fileName, blob);
    }
    
    const content = await zip.generateAsync({ type: 'blob' });
    const fileName = `${selectedFile?.name.replace('.pdf', '') || 'images'}.zip`;
    saveAs(content, fileName);
    toast({ title: 'اكتمل التحميل!', description: `تم تحميل ${fileName}.` });
  };

  const clearAll = () => {
    setSelectedFile(null);
    setConvertedImages([]);
    setError(null);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>تحويل PDF إلى صور (PNG/JPG)</CardTitle>
        <CardDescription>حوّل صفحات ملف PDF إلى صور عالية الجودة. جميع العمليات تتم في متصفحك.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {!pdfLibLoaded && (
          <div className="flex items-center gap-2 p-4 bg-yellow-100 border border-yellow-300 rounded-md">
            <Loader2 className="h-5 w-5 animate-spin text-yellow-700" />
            <span className="text-yellow-800">جاري تحميل مكتبة PDF، يرجى الانتظار...</span>
          </div>
        )}

        <div className="p-6 border-2 border-dashed rounded-lg text-center" onClick={() => fileInputRef.current?.click()}>
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-600">
            {selectedFile ? `الملف المحدد: ${selectedFile.name}` : 'اختر ملف PDF أو اسحبه هنا'}
          </p>
          <input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".pdf" className="hidden" />
        </div>

        {selectedFile && (
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label>صيغة الصورة الناتجة</Label>
              <Select value={imageFormat} onValueChange={(val) => setImageFormat(val as 'png' | 'jpeg')}>
                <SelectTrigger><SelectValue /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="png">PNG (جودة عالية، يدعم الشفافية)</SelectItem>
                  <SelectItem value="jpeg">JPG (حجم أصغر، مناسب للصور)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button onClick={convertPDF} disabled={isConverting || !pdfLibLoaded} className="sm:self-end h-10">
              {isConverting ? <><Loader2 className="ml-2 h-4 w-4 animate-spin" /> {progressText || 'جاري التحويل...'}</> : <>تحويل إلى صور</>}
            </Button>
            <Button onClick={clearAll} variant="outline" className="sm:self-end h-10"><Trash2 className="ml-2 h-4 w-4" /> مسح</Button>
          </div>
        )}

        {error && <Alert variant="destructive"><AlertTriangle className="h-4 w-4" /><AlertDescription>{error}</AlertDescription></Alert>}

        {convertedImages.length > 0 && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold">الصور الناتجة ({convertedImages.length})</h3>
              <Button onClick={downloadAllAsZip}><Archive className="ml-2 h-4 w-4" /> تحميل الكل كـ ZIP</Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {convertedImages.map((image) => (
                <div key={image.pageNumber} className="border rounded-lg p-2 space-y-2">
                  <div className="aspect-w-1 aspect-h-1 bg-gray-100 rounded-md overflow-hidden">
                    <img src={image.dataUrl} alt={`صفحة ${image.pageNumber}`} className="object-contain w-full h-full" />
                  </div>
                  <p className="text-xs text-center text-muted-foreground">صفحة {image.pageNumber}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
