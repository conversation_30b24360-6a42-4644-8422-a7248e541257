
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RefreshCw, Printer, Calendar, MapPin } from 'lucide-react';
import { Textarea } from '../ui/textarea';
import { cn } from '@/lib/utils';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Label } from '../ui/label';

const FormSchema = z.object({
  template: z.enum(['classic', 'modern']).default('classic'),
  introText: z.string().default('بكل الحب والتقدير يتشرف'),
  groomName: z.string().min(1, 'اسم العريس مطلوب.'),
  groomFamilyName: z.string().min(1, 'اسم عائلة العريس مطلوب.'),
  brideFamilyName: z.string().min(1, 'اسم عائلة العروس مطلوب.'),
  eventDay: z.string().default('يوم السبت'),
  eventDate: z.string().default('١٣ / ١١ / ١٤٤٥ هـ'),
  eventLocation: z.string().min(1, 'مكان الحفل مطلوب.'),
  closingText: z.string().default('وبحضوركم يتم لنا الفرح والسرور'),
});

type FormValues = z.infer<typeof FormSchema>;

// Classic Template Component
function ClassicTemplate({ data }: { data: FormValues }) {
    const bgStyle = { backgroundImage: "url('data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 11L89 11L89 89L11 89L11 11Z' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M11 11L89 89' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M89 11L11 89' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M50 11L89 50' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M50 11L11 50' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M50 89L11 50' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M50 89L89 50' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M11 50L50 89' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3Cpath d='M89 50L50 11' fill='none' stroke='%23C0A068' stroke-width='1'/%3E%3C/svg%3E'), linear-gradient(to right, %23333, %23444)" };
    
    return (
        <div className="w-full max-w-md aspect-[5/7] bg-[#333] rounded-2xl shadow-2xl flex invitation-preview overflow-hidden" dir="rtl">
            {/* Main Content Panel - On the right */}
            <div className="w-2/3 p-6 flex flex-col items-center justify-center text-center text-white relative">
                <div className="absolute inset-0 flex items-center justify-center opacity-5 text-9xl text-gray-500 font-serif" style={{ userSelect: 'none' }}>
                    ﷽
                </div>
    
                <div className="z-10 flex flex-col h-full w-full">
                    <h1 className="text-4xl sm:text-5xl font-bold my-4 invitation-title tracking-wider">دعوة خاصة</h1>
                    
                    <p className="text-sm sm:text-base leading-relaxed">{data.introText}</p>
                    <p className="text-2xl sm:text-3xl font-bold text-[#E0C08B] my-2">{data.groomName}</p>
                    <p className="text-sm sm:text-base leading-relaxed">{data.brideFamilyName}</p>
                    
                    <div className="my-auto pt-4 space-y-4 text-xs sm:text-sm">
                        {/* Container for Date and Location */}
                        <div className="flex items-center justify-center gap-4">
                            {/* Location - Right Side */}
                            <div className="text-right">
                                <div className="flex items-center justify-end gap-2 font-bold text-[#E0C08B]">
                                    <span>المكان</span>
                                    <MapPin size={14} />
                                </div>
                                <p>{data.eventLocation}</p>
                            </div>
                            
                            {/* Separator */}
                            <div className="border-l border-gray-500 h-10"></div>
                            
                            {/* Date - Left Side */}
                            <div className="text-right">
                                <div className="flex items-center justify-end gap-2 font-bold text-[#E0C08B]">
                                    <span>التاريخ</span>
                                    <Calendar size={14} />
                                </div>
                                <p>{data.eventDay}</p>
                                <p dir="rtl">{data.eventDate}</p>
                            </div>
                        </div>
                        <div className="border border-gray-500 rounded-full px-4 py-2 text-xs sm:text-sm">
                            {data.closingText}
                        </div>
                    </div>
                    
                    <div className="mt-auto">
                        <p className="text-lg sm:text-xl font-bold text-[#E0C08B]">
                            الداعي / <span className="text-white">{data.groomFamilyName}</span>
                        </p>
                    </div>
                </div>
            </div>
    
            {/* Decorative Panel - On the left */}
            <div className="w-1/3 bg-cover bg-center" style={bgStyle}></div>
      </div>
    );
}

// Modern Template Component
function ModernTemplate({ data }: { data: FormValues }) {
   const bgStyle = { backgroundImage: "url('https://upload.wikimedia.org/wikipedia/commons/thumb/e/e7/Leaves_pattern.svg/1024px-Leaves_pattern.svg.png')", opacity: 0.1 };

  return (
    <div className="w-full max-w-md aspect-[5/7] bg-[#F4F1EC] rounded-2xl shadow-2xl flex flex-col invitation-preview p-8 text-center text-[#5C5C5C] relative overflow-hidden">
      <div className="absolute top-0 left-0 w-full h-full bg-no-repeat bg-center bg-contain" style={bgStyle}></div>
      <div className="z-10 flex flex-col h-full bg-[#F4F1EC]/80 backdrop-blur-sm rounded-lg p-4 -m-4">
        <h2 className="text-lg tracking-[0.2em]">{data.introText}</h2>
        <h1 className="text-5xl font-bold my-4 text-[#A98E6B] invitation-title">{data.groomName}</h1>
        <p className="text-base">و</p>
        <p className="text-lg">{data.brideFamilyName}</p>
        
        <div className="border-y-2 border-[#A98E6B] my-auto py-4">
          <p className="text-lg font-bold">{data.eventDay}</p>
          <p className="text-lg">{data.eventDate}</p>
          <p className="mt-2 text-base">في {data.eventLocation}</p>
        </div>
        
        <p className="mt-auto text-base">{data.closingText}</p>
        <p className="mt-2 font-bold text-lg text-[#A98E6B]">{data.groomFamilyName}</p>
      </div>
    </div>
  );
}

export function WeddingInvitationGeneratorTool() {
  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
        template: 'classic',
        introText: 'بكل الحب والتقدير يتشرف',
        groomName: 'خالد',
        groomFamilyName: 'محمد بن عبدالله الأحمد وأبنائه',
        brideFamilyName: 'كريمة الشيخ / صالح المزيد',
        eventDay: 'يوم السبت',
        eventDate: '١٣ / ١١ / ١٤٤٥ هـ',
        eventLocation: 'قاعة ليلتي للاحتفالات بالرياض',
        closingText: 'وبحضوركم يتم لنا الفرح والسرور',
    },
  });

  const formData = form.watch();

  const handlePrint = () => {
    window.print();
  };
  
  const resetForm = () => {
    form.reset({
        template: 'classic',
        introText: 'بكل الحب والتقدير يتشرف',
        groomName: '',
        groomFamilyName: '',
        brideFamilyName: '',
        eventDay: 'يوم السبت',
        eventDate: 'dd / mm / yyyy',
        eventLocation: '',
        closingText: 'وبحضوركم يتم لنا الفرح والسرور',
    });
  };

  return (
    <>
        <style jsx global>{`
            @import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Tajawal:wght@400;500;700&display=swap');
            
            .invitation-preview {
                font-family: 'Tajawal', sans-serif;
            }
            .invitation-title {
                font-family: 'Amiri', serif;
            }
            @media print {
                body * {
                    visibility: hidden;
                }
                .printable-area, .printable-area * {
                    visibility: visible;
                }
                .printable-area {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    margin: 0;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .invitation-preview {
                    transform: scale(1.1); /* Adjust scale for printing */
                }
                @page {
                    size: A5 portrait;
                    margin: 0;
                }
            }
        `}</style>
        <div className="flex flex-col items-center gap-8 w-full max-w-4xl mx-auto">
            <Card className="w-full print:hidden">
                <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                        <span>بيانات الدعوة</span>
                        <Button variant="ghost" size="sm" onClick={resetForm}><RefreshCw className="ml-2 h-4 w-4" /></Button>
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <Form {...form}>
                        <form className="space-y-4">
                            <FormField
                              control={form.control}
                              name="template"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>اختر تصميم الدعوة</FormLabel>
                                  <FormControl>
                                    <RadioGroup
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                      className="grid grid-cols-2 gap-4"
                                    >
                                      <Label className={cn("border rounded-md p-3 flex items-center justify-center gap-2 cursor-pointer hover:bg-muted/50", field.value === 'classic' && "bg-primary/10 border-primary")}>
                                        <RadioGroupItem value="classic" />
                                        <span>قالب كلاسيكي</span>
                                      </Label>
                                       <Label className={cn("border rounded-md p-3 flex items-center justify-center gap-2 cursor-pointer hover:bg-muted/50", field.value === 'modern' && "bg-primary/10 border-primary")}>
                                        <RadioGroupItem value="modern" />
                                        <span>قالب حديث</span>
                                      </Label>
                                    </RadioGroup>
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <FormField control={form.control} name="introText" render={({ field }) => (<FormItem><FormLabel>العبارة الافتتاحية</FormLabel><FormControl><Textarea {...field} /></FormControl><FormMessage /></FormItem>)} />
                            <FormField control={form.control} name="groomFamilyName" render={({ field }) => (<FormItem><FormLabel>الداعي (اسم عائلة العريس)</FormLabel><FormControl><Input placeholder="مثال: محمد بن عبدالله الأحمد وأبنائه" {...field} /></FormControl><FormMessage /></FormItem>)} />
                            <FormField control={form.control} name="groomName" render={({ field }) => (<FormItem><FormLabel>اسم العريس</FormLabel><FormControl><Input placeholder="مثال: خالد" {...field} /></FormControl><FormMessage /></FormItem>)} />
                            <FormField control={form.control} name="brideFamilyName" render={({ field }) => (<FormItem><FormLabel>اسم عائلة العروس</FormLabel><FormControl><Input placeholder="مثال: كريمة الشيخ / صالح المزيد" {...field} /></FormControl><FormMessage /></FormItem>)} />
                            <div className="grid grid-cols-2 gap-4">
                                <FormField control={form.control} name="eventDay" render={({ field }) => (<FormItem><FormLabel>اليوم</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} />
                                <FormField control={form.control} name="eventDate" render={({ field }) => (<FormItem><FormLabel>التاريخ</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} />
                            </div>
                            <FormField control={form.control} name="eventLocation" render={({ field }) => (<FormItem><FormLabel>المكان</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} />
                            <FormField control={form.control} name="closingText" render={({ field }) => (<FormItem><FormLabel>العبارة الختامية</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} />
                            
                             <Button onClick={handlePrint} className="w-full !mt-6" type="button">
                                <Printer className="ml-2 h-4 w-4" />
                                طباعة الدعوة / حفظ كـ PDF
                            </Button>
                        </form>
                    </Form>
                </CardContent>
            </Card>

            <div className="w-full flex items-center justify-center printable-area">
              {formData.template === 'classic' ? (
                <ClassicTemplate data={formData} />
              ) : (
                <ModernTemplate data={formData} />
              )}
            </div>
        </div>
    </>
  );
}
