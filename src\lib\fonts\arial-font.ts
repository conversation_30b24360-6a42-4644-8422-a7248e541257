'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { saveAs } from 'file-saver';
import { Download, Type, Loader2 } from 'lucide-react';

// Declare global types for PDF-lib
declare global {
  interface Window {
    PDFLib: any;
  }
}

async function createPdfWithArabicText(text: string): Promise<Uint8Array> {
    const { PDFDocument, StandardFonts, rgb } = window.PDFLib;
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage();

    // The Amiri font is a good open-source choice for Arabic
    const fontUrl = 'https://fonts.gstatic.com/s/amiri/v25/J7aRnpd8CGxBHqUpvrM_uA.ttf';
    const fontBytes = await fetch(fontUrl).then(res => res.arrayBuffer());
    pdfDoc.registerFontkit(window.fontkit);
    const customFont = await pdfDoc.embedFont(fontBytes);

    const { width, height } = page.getSize();
    const fontSize = 12;
    const margin = 50;

    // A basic RTL text wrapping implementation
    const processRtlText = (textToProcess: string, maxWidth: number) => {
        const words = textToProcess.split(' ');
        let lines: string[] = [];
        let currentLine = "";
        
        for (const word of words) {
            const lineWithWord = currentLine.length > 0 ? `${word} ${currentLine}` : word;
            const textWidth = customFont.widthOfTextAtSize(lineWithWord, fontSize);
            if (textWidth > maxWidth) {
                lines.push(currentLine);
                currentLine = word;
            } else {
                currentLine = lineWithWord;
            }
        }
        lines.push(currentLine);
        return lines;
    };
    
    const lines = text.split('\n').flatMap(paragraph => processRtlText(paragraph, width - margin * 2));

    let y = height - margin;
    for (const line of lines) {
        if (y < margin) {
            page.addPage();
            y = height - margin;
        }
        page.drawText(line, {
            x: width - margin - customFont.widthOfTextAtSize(line, fontSize),
            y,
            font: customFont,
            size: fontSize,
            color: rgb(0, 0, 0),
        });
        y -= (fontSize * 1.5);
    }
    
    return pdfDoc.save();
}

export function TextToPdfTool() {
  const [text, setText] = useState('');
  const [isConverting, setIsConverting] = useState(false);
  const [libLoaded, setLibLoaded] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (typeof window !== 'undefined' && !window.PDFLib) {
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/pdf-lib@1.17.1/dist/pdf-lib.min.js';
      script.onload = () => {
         const fontkitScript = document.createElement('script');
         fontkitScript.src = 'https://unpkg.com/@pdf-lib/fontkit@1.1.1/dist/fontkit.umd.min.js';
         fontkitScript.onload = () => {
             if (window.PDFLib && window.fontkit) {
                window.PDFLib.PDFDocument.registerFontkit(window.fontkit);
                setLibLoaded(true);
             }
         };
         document.head.appendChild(fontkitScript);
      };
      document.head.appendChild(script);
    } else if (window.PDFLib && window.fontkit) {
      setLibLoaded(true);
    }
  }, []);

  const handleConvertToPdf = async () => {
    if (!text.trim()) {
      toast({ title: "الرجاء إدخال نص أولاً.", variant: "destructive" });
      return;
    }

    setIsConverting(true);
    toast({ title: "جاري إنشاء ملف PDF..." });

    try {
      const pdfBytes = await createPdfWithArabicText(text);
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      saveAs(blob, "document.pdf");
      toast({ title: "تم إنشاء ملف PDF بنجاح!", description: "بدأ تحميل الملف." });
    } catch (error) {
      console.error("PDF generation error:", error);
      toast({ title: "خطأ", description: "حدث خطأ أثناء إنشاء ملف PDF.", variant: "destructive" });
    } finally {
      setIsConverting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>تحويل نص إلى PDF</CardTitle>
        <CardDescription>اكتب أو الصق نصًا هنا لتحويله مباشرةً إلى مستند PDF يدعم اللغة العربية.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!libLoaded && 
            <div className="flex items-center gap-2 p-3 bg-yellow-100 border border-yellow-300 rounded-md">
                <Loader2 className="h-4 w-4 animate-spin"/>
                <span>جاري تحميل المكتبات اللازمة...</span>
            </div>
        }
        <div className="relative">
          <Type className="absolute top-3 right-3 h-5 w-5 text-muted-foreground" />
          <Textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="اكتب أو الصق النص هنا..."
            className="min-h-[300px] text-lg leading-relaxed p-4 pr-10"
          />
        </div>
        <Button onClick={handleConvertToPdf} disabled={isConverting || !libLoaded} className="w-full">
          {isConverting ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <Download className="ml-2 h-4 w-4" />}
          {isConverting ? "جاري التحويل..." : "تحويل وتحميل PDF"}
        </Button>
      </CardContent>
    </Card>
  );
}